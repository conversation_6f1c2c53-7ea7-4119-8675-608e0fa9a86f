<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Flow\Loader;

use Doctrine\ORM\EntityManagerInterface;
use Flow\ETL\Exception\InvalidArgumentException;
use Flow\ETL\FlowContext;
use Flow\ETL\Loader;
use Flow\ETL\Row;
use Flow\ETL\Rows;
use LoginAutonom\CoreBundle\Exception\NotFoundException;
use LoginAutonom\CoreBundle\Interfaces\CommandBusInterface;
use LoginAutonom\DatabaseBundle\DTO\EntityPersistRequest;
use LoginAutonom\DatabaseBundle\DTO\EntityWithContextRequestDTO;
use LoginAutonom\DatabaseBundle\DTO\ObjectsChanges;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\StatusEmbeddable;
use LoginAutonom\DatabaseBundle\Enum\StatusEnum;
use LoginAutonom\DatabaseBundle\Event\Command\PersistEntitiesCommand;
use LoginAutonom\DatabaseBundle\Handler\EntityPersistRequestHandler;
use LoginAutonom\DatabaseBundle\Handler\PersistEntitiesWithContextHandler;
use LoginAutonom\DatabaseBundle\Storage\EntityPersistContextStorage;
use LoginAutonom\EtlBundle\Enum\EtlCommonFieldNamesEnum;
use LoginAutonom\EtlBundle\Enum\EtlProcessStatusEnum;
use LoginAutonom\EtlBundle\Interfaces\EtlCacheAwareInterface;
use LoginAutonom\EtlBundle\Interfaces\EtlProcessAwareInterface;
use LoginAutonom\EtlBundle\Trait\EtlCacheAwareTrait;
use LoginAutonom\EtlBundle\Trait\EtlCommonFunctionsTrait;
use LoginAutonom\EtlBundle\Trait\EtlProcessAwareTrait;
use LoginAutonom\EtlBundle\Trait\EtlSerializableTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

final class EntitiesFromRowEntryLoader implements
    Loader,
    LoggerAwareInterface,
    EtlCacheAwareInterface,
    EtlProcessAwareInterface
{
    use LoggerAwareTrait;
    use EtlCacheAwareTrait;
    use EtlCommonFunctionsTrait;
    use EtlProcessAwareTrait;
    use EtlSerializableTrait;

    private const ENTITIES = 'entities';
    private const ENTITY_CONTEXT_MAP = 'entityContextMap';
    private const LAST_ROW_IDENTIFIERS = 'lastRowIdentifiers';

    public function __construct(
        private readonly array $fieldNames,
        private readonly array $persistReasons,
        private readonly array $lastRowIdentifiers,
        private readonly EntityPersistRequestHandler $entityPersistRequestHandler,
        private readonly CommandBusInterface $commandBus,
        private readonly PersistEntitiesWithContextHandler $persistEntitiesWithContextHandler,
    ) {
    }

    public function load(Rows $rows, FlowContext $context): void
    {
        $this->logger->info('Starting loader: ' . get_class($this), ['rows' => $rows->count()]);

        $entitiesData = $this->collectEntitiesFromRows($rows);
        $entities = $entitiesData[self::ENTITIES];
        $entityContextMap = $entitiesData[self::ENTITY_CONTEXT_MAP];
        $lastRowIdentifiers = $entitiesData[self::LAST_ROW_IDENTIFIERS];

        if (empty($entities)) {
            return;
        }

        $this->logger->info('Entities count: ' . count($entities));
        $this->prepareProcess($lastRowIdentifiers);

        $entityRequests = $this->collectEntityWithContextRequests($entities, $entityContextMap);
        $this->persistEntitiesWithContextHandler->handle($entityRequests);

        $this->logger->info('Stopping loader: ' . get_class($this));
    }

    private function collectEntitiesFromRows(Rows $rows): array
    {
        $entities = [];
        $entityContextMap = [];
        $lastRowIdentifiers = [];

        /** @var Row $row */
        foreach ($rows as $row) {
            $this->collectEntitiesFromFields($row, $entities);
            $this->collectEntitiesFromChanges($row, $entities);
            $this->collectEntityContexts($row, $entities, $entityContextMap);

            $lastRowIdentifiers = $this->extractLastRowIdentifiers($row);
        }

        return [
            self::ENTITIES => $entities,
            self::ENTITY_CONTEXT_MAP => $entityContextMap,
            self::LAST_ROW_IDENTIFIERS => $lastRowIdentifiers
        ];
    }

    private function collectEntityWithContextRequests(array $entities, array $entityContextMap): array
    {
        $entityRequests = [];
        foreach ($entities as $entityHash => $entity) {
            $context = $entityContextMap[$entityHash] ?? null;
            $reason = $this->persistReasons[get_class($entity)];
            $entityRequests[] = new EntityWithContextRequestDTO(
                $entity,
                $context,
                $reason
            );
        }

        return $entityRequests;
    }

    private function collectEntitiesFromFields(Row $row, array &$entities): void
    {
        foreach ($this->fieldNames as $fieldName) {
            try {
                $entity = $row->valueOf($fieldName);
                $entities[spl_object_hash($entity)] = $entity;
            } catch (InvalidArgumentException $e) {
            }
        }
    }

    private function collectEntitiesFromChanges(Row $row, array &$entities): void
    {
        try {
            $changeStorage = $this->getChangesStorage($row);
            $this->addEntitiesToCollection($changeStorage->getNew(), $entities);
            $this->addEntitiesToCollection($changeStorage->getChanged(), $entities);
            $this->handleDeletedEntities($changeStorage->getDeleted(), $entities);
        } catch (NotFoundException $e) {
        }
    }

    private function addEntitiesToCollection(array $entitiesArray, array &$entities): void
    {
        foreach ($entitiesArray as $entity) {
            $entities[spl_object_hash($entity)] = $entity;
        }
    }

    private function handleDeletedEntities(array $deletedEntities, array &$entities): void
    {
        $deletedStatus = (new StatusEmbeddable())->setStatus(StatusEnum::DELETED);
        foreach ($deletedEntities as $entity) {
            if (method_exists($entity, 'setStatus')) {
                $entity->setStatus($deletedStatus);
            }

            $entities[spl_object_hash($entity)] = $entity;
        }
    }

    private function collectEntityContexts(Row $row, array $entities, array &$entityContextMap): void
    {
        try {
            $entityPersistContextStorage = $this->getEntityPersistContextStorage($row);
            foreach ($entities as $entityHash => $entity) {
                if ($entityPersistContextStorage->has($entity)) {
                    $entityContextMap[$entityHash] = $entityPersistContextStorage->get($entity);
                }
            }
        } catch (NotFoundException $e) {
        }
    }

    private function extractLastRowIdentifiers(Row $row): array
    {
        $rowArray = $row->toArray();

        return array_intersect_key(
            $rowArray,
            array_flip($this->lastRowIdentifiers)
        );
    }

    private function prepareProcess(array $lastRowIdentifiers): void
    {
        $this->process->mergeMetadata($lastRowIdentifiers);
        $this->process->setProcessStatus(EtlProcessStatusEnum::SUCCESSFUL);

        $this->entityPersistRequestHandler->handle(
            new EntityPersistRequest(
                $this->process,
                'New transaction in ETL load'
            )
        );

        $this->commandBus->send(
            new PersistEntitiesCommand([$this->process])
        );
    }

    private function getChangesStorage(Row $row): ObjectsChanges
    {
        if ($row->has(EtlCommonFieldNamesEnum::CHANGES_STORAGE)) {
            return $row->get(EtlCommonFieldNamesEnum::CHANGES_STORAGE)->value();
        }

        throw new NotFoundException('In current row', EtlCommonFieldNamesEnum::CHANGES_STORAGE);
    }

    private function getEntityPersistContextStorage(Row $row): EntityPersistContextStorage
    {
        if ($row->has(EtlCommonFieldNamesEnum::ENTITY_PERSIST_CONTEXT_STORAGE)) {
            return $row->get(EtlCommonFieldNamesEnum::ENTITY_PERSIST_CONTEXT_STORAGE)->value();
        }

        throw new NotFoundException('In current row', EtlCommonFieldNamesEnum::ENTITY_PERSIST_CONTEXT_STORAGE);
    }
}
