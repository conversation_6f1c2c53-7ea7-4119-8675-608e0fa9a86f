<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\DTO;

final class EntityPersistContext
{
    private array $context;
    private object $entity;

    public function getContext(): array
    {
        return $this->context;
    }

    public function hasContext(): bool
    {
        return isset($this->context);
    }

    public function setContext(array $context): void
    {
        $this->context = $context;
    }

    public function getEntity(): object
    {
        return $this->entity;
    }

    public function hasEntity(): bool
    {
        return isset($this->entity);
    }

    public function setEntity(object $entity): void
    {
        $this->entity = $entity;
    }
}
