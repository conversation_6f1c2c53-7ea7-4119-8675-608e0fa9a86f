<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\DTO;

final class EntityPersistContextDTO
{
    private object $entity;
    private ?EntityPersistContext $entityPersistContext;

    public function __construct($entity, object $entityPersistContext = null)
    {
        $this->entity = $entity;
        $this->entityPersistContext = $entityPersistContext;
    }

    public function getEntity(): object
    {
        return $this->entity;
    }

    public function hasEntity(): bool
    {
        return isset($this->entity);
    }

    public function setEntity(object $entity): void
    {
        $this->entity = $entity;
    }

    public function getEntityPersistContext(): ?EntityPersistContext
    {
        return $this->entityPersistContext;
    }

    public function hasEntityPersistContext(): bool
    {
        return isset($this->entityPersistContext);
    }

    public function setEntityPersistContext(?EntityPersistContext $entityPersistContext): void
    {
        $this->entityPersistContext = $entityPersistContext;
    }
}
