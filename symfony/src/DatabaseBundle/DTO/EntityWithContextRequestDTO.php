<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\DTO;

final readonly class EntityWithContextRequestDTO
{
    public function __construct(
        private object $entity,
        private ?EntityPersistContext $context,
        private string $reason,
    ) {
    }

    public function getEntity(): object
    {
        return $this->entity;
    }

    public function getContext(): ?EntityPersistContext
    {
        return $this->context;
    }

    public function getReason(): string
    {
        return $this->reason;
    }

    public function hasContext(): bool
    {
        return isset($this->context);
    }
}
