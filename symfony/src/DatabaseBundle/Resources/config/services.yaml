parameters:
  database.persist_entities_chunk_size: 100

services:
  _defaults:
    autowire: true
    autoconfigure: true

  LoginAutonom\DatabaseBundle\:
    resource: '../../'
    exclude:
      - '../../DependencyInjection/'
      - '../../Entity/'
      - '../../Kernel.php'

  <PERSON>\Uuid\Doctrine\UuidV7Generator:
    class: <PERSON>\Uuid\Doctrine\UuidV7Generator
    tags:
      - { name: "doctrine.id_generator" }

  <PERSON>\Uuid\Doctrine\UuidGenerator:
    class: <PERSON>\Uuid\Doctrine\UuidGenerator
    tags:
      - { name: "doctrine.id_generator" }

  LoginAutonom\DatabaseBundle\Handler\PersistEntitiesWithContextHandler:
    arguments:
      $chunkSize: '%database.persist_entities_chunk_size%'
