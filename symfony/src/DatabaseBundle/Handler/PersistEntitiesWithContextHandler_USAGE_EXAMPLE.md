# PersistEntitiesWithContextHandler Usage Example

## Old Usage (Before Changes)
```php
// OLD - This no longer works
$handler->handle($entities, $entityContextMap, $persistReasons);
```

## New Usage (After Changes)
```php
use LoginAutonom\DatabaseBundle\DTO\EntityWithContextRequest;

// Create array of EntityWithContextRequest DTOs
$entityRequests = [];

foreach ($entities as $entity) {
    $context = $entityContextMap[$entityHash] ?? null;
    $reason = $persistReasons[get_class($entity)];
    
    $entityRequests[] = new EntityWithContextRequest(
        entity: $entity,
        context: $context,
        reason: $reason
    );
}

// Call handler with DTO array
$handler->handle($entityRequests);
```

## Benefits of New Approach

1. **Single DTO Parameter**: Instead of 3 separate arrays that need to be correlated by hash/class, everything is contained in a single DTO per entity.

2. **No Entity Grouping**: Entities are no longer grouped by class, eliminating unnecessary complexity.

3. **Same-Loop Processing**: Entity persist requests and persistence commands are now handled in the same loop, fixing the transaction issue.

4. **Configurable Chunk Size**: Chunk size is now configurable via YAML parameter `database.persist_entities_chunk_size` instead of hardcoded constant.

## Configuration

The chunk size can be configured in `symfony/src/DatabaseBundle/Resources/config/services.yaml`:

```yaml
parameters:
  database.persist_entities_chunk_size: 100  # Change this value as needed
```
