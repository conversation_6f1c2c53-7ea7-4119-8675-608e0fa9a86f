<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Handler;

use LoginAutonom\CoreBundle\Interfaces\CommandBusInterface;
use LoginAutonom\DatabaseBundle\DTO\EntityPersistRequest;
use LoginAutonom\DatabaseBundle\DTO\EntityWithContextRequest;
use LoginAutonom\DatabaseBundle\Event\Command\PersistEntitiesCommand;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

final class PersistEntitiesWithContextHandler implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    public function __construct(
        private readonly EntityPersistRequestHandler $entityPersistRequestHandler,
        private readonly CommandBusInterface $commandBus,
        private readonly TransactionHandler $transactionHandler,
        private readonly int $chunkSize,
    ) {
    }

    /**
     * @param EntityWithContextRequest[] $entityRequests
     */
    public function handle(array $entityRequests): void
    {
        if (empty($entityRequests)) {
            return;
        }

        $this->logger->info('Starting entity persistence with context', [
            'entity_count' => count($entityRequests),
            'chunk_size' => $this->chunkSize,
        ]);

        $this->processEntityRequests($entityRequests);

        $this->logger->info('Completed entity persistence with context');
    }

    /**
     * @param EntityWithContextRequest[] $entityRequests
     */
    private function processEntityRequests(array $entityRequests): void
    {
        $entities = [];
        $chunkNumber = 1;

        foreach ($entityRequests as $request) {
            $entityPersistRequest = new EntityPersistRequest($request->getEntity(), $request->getReason());
            if ($request->hasContext()) {
                $entityPersistRequest->setEntityPersistContext($request->getContext());
            }
            $this->entityPersistRequestHandler->handle($entityPersistRequest);

            $entities[] = $request->getEntity();

            if (count($entities) >= $this->chunkSize) {
                $this->persistEntitiesChunk($entities, $chunkNumber);
                $entities = [];
                $chunkNumber++;
            }
        }

        // Persist remaining entities if any
        if (!empty($entities)) {
            $this->persistEntitiesChunk($entities, $chunkNumber);
        }
    }

    private function persistEntitiesChunk(array $entitiesChunk, int $chunkNumber): void
    {
        $logData = [
            'chunk_number' => $chunkNumber,
            'chunk_size' => count($entitiesChunk),
        ];

        $this->logger->info('Persisting entities chunk', $logData);

        $this->commandBus->send(
            new PersistEntitiesCommand($entitiesChunk)
        );

        $this->transactionHandler->commitTransaction();
        $this->transactionHandler->startTransaction();
    }
}
